#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
东方财富榜单数据爬取与可视化系统
主程序入口
"""

import argparse
import sys
import logging
from pathlib import Path
import time
import signal
import json

from eastmoney_crawler import EastMoneyCrawler
from data_manager import DataManager
from visualizer import RankingVisualizer
from scheduler import RankingScheduler
from web_dashboard import app

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('main.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class RankingSystem:
    """榜单数据系统主类"""
    
    def __init__(self):
        self.crawler = EastMoneyCrawler()
        self.data_manager = DataManager()
        self.visualizer = RankingVisualizer()
        self.scheduler = RankingScheduler()
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """信号处理函数"""
        logger.info(f"收到信号 {signum}，正在关闭系统...")
        self.cleanup()
        sys.exit(0)
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.scheduler.is_running:
                self.scheduler.stop()
            logger.info("系统清理完成")
        except Exception as e:
            logger.error(f"清理资源失败: {e}")
    
    def run_once(self):
        """运行一次数据更新"""
        try:
            logger.info("开始单次数据更新...")
            
            # 爬取数据
            rankings = self.crawler.get_all_rankings()
            
            if not rankings:
                logger.warning("未获取到任何榜单数据")
                return False
            
            # 保存数据
            self.data_manager.save_rankings(rankings)
            
            # 创建可视化图表
            self.create_charts(rankings)
            
            logger.info("单次数据更新完成")
            return True
            
        except Exception as e:
            logger.error(f"单次数据更新失败: {e}")
            return False
    
    def create_charts(self, rankings):
        """创建可视化图表"""
        try:
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            
            for ranking_type, df in rankings.items():
                if df is None or df.empty:
                    continue
                
                # 创建表格
                table_fig = self.visualizer.create_ranking_table(df, ranking_type)
                if table_fig:
                    self.visualizer.save_chart(
                        table_fig, 
                        f"{ranking_type}_table_{timestamp}", 
                        'html'
                    )
                
                # 创建柱状图
                bar_fig = self.visualizer.create_bar_chart(df, ranking_type)
                if bar_fig:
                    self.visualizer.save_chart(
                        bar_fig, 
                        f"{ranking_type}_bar_{timestamp}", 
                        'html'
                    )
            
            logger.info("可视化图表创建完成")
            
        except Exception as e:
            logger.error(f"创建图表失败: {e}")
    
    def run_scheduler(self):
        """运行定时任务"""
        try:
            logger.info("启动定时任务系统...")
            self.scheduler.start()
            
            # 保持运行
            while True:
                time.sleep(60)
                
        except KeyboardInterrupt:
            logger.info("收到停止信号")
        finally:
            self.cleanup()
    
    def run_web_dashboard(self, host="0.0.0.0", port=8050, debug=False):
        """运行Web仪表板"""
        try:
            logger.info(f"启动Web仪表板，地址: http://{host}:{port}")
            app.run(debug=debug, host=host, port=port)
        except Exception as e:
            logger.error(f"启动Web仪表板失败: {e}")
    
    def show_status(self):
        """显示系统状态"""
        try:
            status = self.scheduler.get_status()
            summary = self.data_manager.get_data_summary()
            
            print("\n=== 系统状态 ===")
            print(f"运行状态: {'运行中' if status['is_running'] else '已停止'}")
            print(f"最后更新: {status['last_update'] or '无'}")
            print(f"更新次数: {status['update_count']}")
            print(f"下次更新: {status['next_update'] or '无'}")
            
            print("\n=== 数据摘要 ===")
            for ranking_type, info in summary.items():
                print(f"{ranking_type.title()}榜:")
                print(f"  总记录数: {info['total_records']}")
                print(f"  最新更新: {info['latest_update'] or '无'}")
            
            print("\n=== 配置信息 ===")
            config = status['config']
            print(f"更新间隔: {config['update_interval_minutes']}分钟")
            print(f"交易时间: {config['trading_hours']['start']} - {config['trading_hours']['end']}")
            print(f"数据保留: {config['auto_cleanup_days']}天")
            
        except Exception as e:
            logger.error(f"获取状态失败: {e}")
    
    def export_data(self, ranking_type, format_type='csv', days=None):
        """导出数据"""
        try:
            filepath = self.data_manager.export_data(ranking_type, format_type, days)
            if filepath:
                print(f"数据已导出到: {filepath}")
            else:
                print("导出失败")
        except Exception as e:
            logger.error(f"导出数据失败: {e}")
    
    def test_crawler(self):
        """测试爬虫"""
        try:
            print("测试爬虫功能...")
            rankings = self.crawler.get_all_rankings()
            
            for ranking_type, df in rankings.items():
                print(f"\n{ranking_type.title()}榜:")
                if df is not None and not df.empty:
                    print(f"数据形状: {df.shape}")
                    print("前5条记录:")
                    print(df.head().to_string())
                else:
                    print("无数据")
                    
        except Exception as e:
            logger.error(f"测试爬虫失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="东方财富榜单数据爬取与可视化系统")
    parser.add_argument('--mode', choices=['once', 'scheduler', 'web', 'status', 'test', 'export'], 
                       default='web', help='运行模式')
    parser.add_argument('--host', default='0.0.0.0', help='Web服务器主机地址')
    parser.add_argument('--port', type=int, default=8050, help='Web服务器端口')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--ranking-type', choices=['popularity', 'soaring', 'topic'], 
                       help='导出数据的榜单类型')
    parser.add_argument('--format', choices=['csv', 'excel'], default='csv', 
                       help='导出数据格式')
    parser.add_argument('--days', type=int, help='导出历史数据的天数')
    
    args = parser.parse_args()
    
    # 创建系统实例
    system = RankingSystem()
    
    try:
        if args.mode == 'once':
            # 单次运行
            success = system.run_once()
            sys.exit(0 if success else 1)
            
        elif args.mode == 'scheduler':
            # 定时任务模式
            system.run_scheduler()
            
        elif args.mode == 'web':
            # Web仪表板模式
            system.run_web_dashboard(args.host, args.port, args.debug)
            
        elif args.mode == 'status':
            # 显示状态
            system.show_status()
            
        elif args.mode == 'test':
            # 测试模式
            system.test_crawler()
            
        elif args.mode == 'export':
            # 导出模式
            if not args.ranking_type:
                print("错误: 导出模式需要指定 --ranking-type 参数")
                sys.exit(1)
            system.export_data(args.ranking_type, args.format, args.days)
            
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序运行失败: {e}")
        sys.exit(1)
    finally:
        system.cleanup()

if __name__ == "__main__":
    main() 