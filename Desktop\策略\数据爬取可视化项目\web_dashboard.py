import dash
from dash import dcc, html, Input, Output, callback
import dash_bootstrap_components as dbc
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
from datetime import datetime, timedelta
import json
from pathlib import Path
import logging

from eastmoney_crawler import EastMoneyCrawler
from data_manager import DataManager
from visualizer import RankingVisualizer
from scheduler import RankingScheduler

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 初始化组件
crawler = EastMoneyCrawler()
data_manager = DataManager()
visualizer = RankingVisualizer()
scheduler = RankingScheduler()

# 创建Dash应用
app = dash.Dash(__name__, external_stylesheets=[dbc.themes.BOOTSTRAP])
app.title = "东方财富榜单数据可视化"

# 应用布局
app.layout = dbc.Container([
    # 标题栏
    dbc.Row([
        dbc.Col([
            html.H1("东方财富榜单数据可视化", className="text-center mb-4"),
            html.Hr()
        ])
    ]),
    
    # 控制面板
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("控制面板"),
                dbc.CardBody([
                    dbc.Row([
                        dbc.Col([
                            dbc.Button("立即更新数据", id="update-btn", color="primary", className="me-2"),
                            dbc.Button("启动定时任务", id="start-scheduler-btn", color="success", className="me-2"),
                            dbc.Button("停止定时任务", id="stop-scheduler-btn", color="danger", className="me-2"),
                        ]),
                        dbc.Col([
                            html.Div(id="status-display", className="text-muted")
                        ])
                    ]),
                    dbc.Row([
                        dbc.Col([
                            html.Label("更新间隔(分钟):"),
                            dcc.Input(id="interval-input", type="number", value=30, min=1, max=1440)
                        ], width=3),
                        dbc.Col([
                            html.Label("数据保留天数:"),
                            dcc.Input(id="retention-input", type="number", value=30, min=1, max=365)
                        ], width=3),
                        dbc.Col([
                            dbc.Button("保存配置", id="save-config-btn", color="info")
                        ], width=3)
                    ], className="mt-3")
                ])
            ])
        ])
    ], className="mb-4"),
    
    # 标签页
    dbc.Tabs([
        # 实时数据标签页
        dbc.Tab([
            dbc.Row([
                dbc.Col([
                    html.H4("人气榜", className="text-center"),
                    dcc.Graph(id="popularity-table")
                ], width=4),
                dbc.Col([
                    html.H4("飙升榜", className="text-center"),
                    dcc.Graph(id="soaring-table")
                ], width=4),
                dbc.Col([
                    html.H4("话题榜", className="text-center"),
                    dcc.Graph(id="topic-table")
                ], width=4)
            ]),
            dbc.Row([
                dbc.Col([
                    dcc.Graph(id="popularity-bar")
                ], width=4),
                dbc.Col([
                    dcc.Graph(id="soaring-bar")
                ], width=4),
                dbc.Col([
                    dcc.Graph(id="topic-bar")
                ], width=4)
            ], className="mt-4")
        ], label="实时数据", tab_id="realtime"),
        
        # 历史数据标签页
        dbc.Tab([
            dbc.Row([
                dbc.Col([
                    html.Label("选择时间范围:"),
                    dcc.Dropdown(
                        id="time-range-dropdown",
                        options=[
                            {"label": "最近1天", "value": 1},
                            {"label": "最近3天", "value": 3},
                            {"label": "最近7天", "value": 7},
                            {"label": "最近30天", "value": 30}
                        ],
                        value=7
                    )
                ], width=3),
                dbc.Col([
                    html.Label("选择榜单类型:"),
                    dcc.Dropdown(
                        id="ranking-type-dropdown",
                        options=[
                            {"label": "人气榜", "value": "popularity"},
                            {"label": "飙升榜", "value": "soaring"},
                            {"label": "话题榜", "value": "topic"}
                        ],
                        value="popularity"
                    )
                ], width=3),
                dbc.Col([
                    dbc.Button("刷新历史数据", id="refresh-history-btn", color="primary")
                ], width=3)
            ], className="mb-4"),
            dbc.Row([
                dbc.Col([
                    dcc.Graph(id="historical-chart")
                ])
            ])
        ], label="历史数据", tab_id="historical"),
        
        # 数据统计标签页
        dbc.Tab([
            dbc.Row([
                dbc.Col([
                    dbc.Card([
                        dbc.CardHeader("数据摘要"),
                        dbc.CardBody(id="data-summary")
                    ])
                ], width=6),
                dbc.Col([
                    dbc.Card([
                        dbc.CardHeader("系统状态"),
                        dbc.CardBody(id="system-status")
                    ])
                ], width=6)
            ]),
            dbc.Row([
                dbc.Col([
                    dcc.Graph(id="comparison-chart")
                ])
            ], className="mt-4")
        ], label="数据统计", tab_id="statistics"),
        
        # 设置标签页
        dbc.Tab([
            dbc.Row([
                dbc.Col([
                    dbc.Card([
                        dbc.CardHeader("定时任务设置"),
                        dbc.CardBody([
                            html.Label("更新间隔(分钟):"),
                            dcc.Input(id="config-interval", type="number", value=30, className="mb-3"),
                            html.Label("交易时间开始:"),
                            dcc.Input(id="trading-start", type="text", value="09:30", className="mb-3"),
                            html.Label("交易时间结束:"),
                            dcc.Input(id="trading-end", type="text", value="15:00", className="mb-3"),
                            html.Label("数据保留天数:"),
                            dcc.Input(id="config-retention", type="number", value=30, className="mb-3"),
                            dbc.Button("保存设置", id="save-settings-btn", color="primary")
                        ])
                    ])
                ], width=6),
                dbc.Col([
                    dbc.Card([
                        dbc.CardHeader("数据导出"),
                        dbc.CardBody([
                            html.Label("选择榜单:"),
                            dcc.Dropdown(
                                id="export-ranking-dropdown",
                                options=[
                                    {"label": "人气榜", "value": "popularity"},
                                    {"label": "飙升榜", "value": "soaring"},
                                    {"label": "话题榜", "value": "topic"}
                                ],
                                value="popularity"
                            ),
                            html.Label("选择格式:"),
                            dcc.Dropdown(
                                id="export-format-dropdown",
                                options=[
                                    {"label": "CSV", "value": "csv"},
                                    {"label": "Excel", "value": "excel"}
                                ],
                                value="csv"
                            ),
                            dbc.Button("导出数据", id="export-btn", color="success", className="mt-3")
                        ])
                    ])
                ], width=6)
            ])
        ], label="设置", tab_id="settings")
    ], id="tabs"),
    
    # 自动刷新
    dcc.Interval(
        id='interval-component',
        interval=60*1000,  # 每分钟刷新一次
        n_intervals=0
    )
], fluid=True)

# 回调函数：更新状态显示
@app.callback(
    Output("status-display", "children"),
    [Input("update-btn", "n_clicks"),
     Input("start-scheduler-btn", "n_clicks"),
     Input("stop-scheduler-btn", "n_clicks"),
     Input("interval-component", "n_intervals")]
)
def update_status(update_clicks, start_clicks, stop_clicks, n_intervals):
    status = scheduler.get_status()
    return f"状态: {'运行中' if status['is_running'] else '已停止'} | " \
           f"最后更新: {status['last_update'] or '无'} | " \
           f"更新次数: {status['update_count']}"

# 回调函数：立即更新数据
@app.callback(
    Output("update-btn", "children"),
    [Input("update-btn", "n_clicks")]
)
def update_data(n_clicks):
    if n_clicks:
        try:
            scheduler.update_rankings()
            return "更新完成"
        except Exception as e:
            logger.error(f"更新数据失败: {e}")
            return "更新失败"
    return "立即更新数据"

# 回调函数：启动定时任务
@app.callback(
    Output("start-scheduler-btn", "children"),
    [Input("start-scheduler-btn", "n_clicks")]
)
def start_scheduler(n_clicks):
    if n_clicks:
        try:
            scheduler.start()
            return "已启动"
        except Exception as e:
            logger.error(f"启动定时任务失败: {e}")
            return "启动失败"
    return "启动定时任务"

# 回调函数：停止定时任务
@app.callback(
    Output("stop-scheduler-btn", "children"),
    [Input("stop-scheduler-btn", "n_clicks")]
)
def stop_scheduler(n_clicks):
    if n_clicks:
        try:
            scheduler.stop()
            return "已停止"
        except Exception as e:
            logger.error(f"停止定时任务失败: {e}")
            return "停止失败"
    return "停止定时任务"

# 回调函数：更新实时数据表格
@app.callback(
    [Output("popularity-table", "figure"),
     Output("soaring-table", "figure"),
     Output("topic-table", "figure")],
    [Input("interval-component", "n_intervals")]
)
def update_tables(n_intervals):
    try:
        rankings = data_manager.get_latest_rankings()
        
        popularity_fig = visualizer.create_ranking_table(rankings.get('popularity', pd.DataFrame()), 'popularity')
        soaring_fig = visualizer.create_ranking_table(rankings.get('soaring', pd.DataFrame()), 'soaring')
        topic_fig = visualizer.create_ranking_table(rankings.get('topic', pd.DataFrame()), 'topic')
        
        return popularity_fig, soaring_fig, topic_fig
    except Exception as e:
        logger.error(f"更新表格失败: {e}")
        return go.Figure(), go.Figure(), go.Figure()

# 回调函数：更新柱状图
@app.callback(
    [Output("popularity-bar", "figure"),
     Output("soaring-bar", "figure"),
     Output("topic-bar", "figure")],
    [Input("interval-component", "n_intervals")]
)
def update_bars(n_intervals):
    try:
        rankings = data_manager.get_latest_rankings()
        
        popularity_fig = visualizer.create_bar_chart(rankings.get('popularity', pd.DataFrame()), 'popularity')
        soaring_fig = visualizer.create_bar_chart(rankings.get('soaring', pd.DataFrame()), 'soaring')
        topic_fig = visualizer.create_bar_chart(rankings.get('topic', pd.DataFrame()), 'topic')
        
        return popularity_fig, soaring_fig, topic_fig
    except Exception as e:
        logger.error(f"更新柱状图失败: {e}")
        return go.Figure(), go.Figure(), go.Figure()

# 回调函数：更新历史数据图表
@app.callback(
    Output("historical-chart", "figure"),
    [Input("time-range-dropdown", "value"),
     Input("ranking-type-dropdown", "value"),
     Input("refresh-history-btn", "n_clicks")]
)
def update_historical_chart(days, ranking_type, n_clicks):
    try:
        if days and ranking_type:
            historical_data = data_manager.get_historical_data(ranking_type, days)
            
            if not historical_data.empty:
                # 创建时间序列图
                historical_data['created_at'] = pd.to_datetime(historical_data['created_at'])
                
                if ranking_type in ['popularity', 'soaring']:
                    score_col = f'{ranking_type}_score'
                    group_data = historical_data.groupby('created_at')[score_col].mean().reset_index()
                    
                    fig = go.Figure()
                    fig.add_trace(go.Scatter(
                        x=group_data['created_at'],
                        y=group_data[score_col],
                        mode='lines+markers',
                        name=f'{ranking_type.title()}指数'
                    ))
                    
                    fig.update_layout(
                        title=f"{ranking_type.title()}指数历史趋势",
                        xaxis_title="时间",
                        yaxis_title="平均指数"
                    )
                    
                elif ranking_type == 'topic':
                    group_data = historical_data.groupby('created_at')['hot_score'].mean().reset_index()
                    
                    fig = go.Figure()
                    fig.add_trace(go.Scatter(
                        x=group_data['created_at'],
                        y=group_data['hot_score'],
                        mode='lines+markers',
                        name='热度指数'
                    ))
                    
                    fig.update_layout(
                        title="话题热度历史趋势",
                        xaxis_title="时间",
                        yaxis_title="平均热度指数"
                    )
                
                return fig
        
        return go.Figure()
    except Exception as e:
        logger.error(f"更新历史图表失败: {e}")
        return go.Figure()

# 回调函数：更新数据摘要
@app.callback(
    Output("data-summary", "children"),
    [Input("interval-component", "n_intervals")]
)
def update_data_summary(n_intervals):
    try:
        summary = data_manager.get_data_summary()
        
        summary_html = []
        for ranking_type, info in summary.items():
            summary_html.append(html.H5(f"{ranking_type.title()}榜"))
            summary_html.append(html.P(f"总记录数: {info['total_records']}"))
            summary_html.append(html.P(f"最新更新: {info['latest_update'] or '无'}"))
            summary_html.append(html.Hr())
        
        return summary_html
    except Exception as e:
        logger.error(f"更新数据摘要失败: {e}")
        return html.P("获取数据摘要失败")

# 回调函数：更新系统状态
@app.callback(
    Output("system-status", "children"),
    [Input("interval-component", "n_intervals")]
)
def update_system_status(n_intervals):
    try:
        status = scheduler.get_status()
        
        status_html = [
            html.P(f"运行状态: {'运行中' if status['is_running'] else '已停止'}"),
            html.P(f"最后更新: {status['last_update'] or '无'}"),
            html.P(f"更新次数: {status['update_count']}"),
            html.P(f"下次更新: {status['next_update'] or '无'}"),
            html.Hr(),
            html.H6("配置信息:"),
            html.P(f"更新间隔: {status['config']['update_interval_minutes']}分钟"),
            html.P(f"交易时间: {status['config']['trading_hours']['start']} - {status['config']['trading_hours']['end']}")
        ]
        
        return status_html
    except Exception as e:
        logger.error(f"更新系统状态失败: {e}")
        return html.P("获取系统状态失败")

if __name__ == "__main__":
    app.run(debug=True, host="0.0.0.0", port=8050) 