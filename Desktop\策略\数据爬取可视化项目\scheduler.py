import schedule
import time
import logging
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Optional
import threading
import json
from pathlib import Path

from eastmoney_crawler import EastMoneyCrawler
from data_manager import DataManager
from visualizer import RankingVisualizer

logger = logging.getLogger(__name__)

class RankingScheduler:
    """榜单数据定时任务管理器"""
    
    def __init__(self, config_file: str = "scheduler_config.json"):
        self.config_file = Path(config_file)
        self.crawler = EastMoneyCrawler()
        self.data_manager = DataManager()
        self.visualizer = RankingVisualizer()
        
        # 加载配置
        self.config = self.load_config()
        
        # 任务状态
        self.is_running = False
        self.last_update = None
        self.update_count = 0
        
        # 创建日志目录
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        
        # 设置日志文件
        self.setup_logging()
    
    def load_config(self) -> Dict:
        """加载配置文件"""
        default_config = {
            "update_interval_minutes": 30,
            "trading_hours": {
                "start": "09:30",
                "end": "15:00"
            },
            "auto_cleanup_days": 30,
            "save_charts": True,
            "chart_formats": ["html", "png"],
            "notifications": {
                "enabled": False,
                "email": "",
                "webhook": ""
            }
        }
        
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合并默认配置
                    for key, value in default_config.items():
                        if key not in config:
                            config[key] = value
                    return config
            except Exception as e:
                logger.error(f"加载配置文件失败: {e}")
                return default_config
        else:
            # 创建默认配置文件
            self.save_config(default_config)
            return default_config
    
    def save_config(self, config: Dict):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            logger.info("配置文件已保存")
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
    
    def setup_logging(self):
        """设置日志"""
        log_file = self.log_dir / f"scheduler_{datetime.now().strftime('%Y%m%d')}.log"
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.setLevel(logging.INFO)
    
    def is_trading_time(self) -> bool:
        """检查是否在交易时间内"""
        now = datetime.now()
        current_time = now.strftime('%H:%M')
        
        # 检查是否为工作日
        if now.weekday() >= 5:  # 周六、周日
            return False
        
        # 检查是否在交易时间内
        start_time = self.config['trading_hours']['start']
        end_time = self.config['trading_hours']['end']
        
        return start_time <= current_time <= end_time
    
    def update_rankings(self):
        """更新榜单数据"""
        try:
            logger.info("开始更新榜单数据...")
            
            # 检查是否在交易时间
            if not self.is_trading_time():
                logger.info("当前不在交易时间，跳过更新")
                return
            
            # 爬取数据
            rankings = self.crawler.get_all_rankings()
            
            if not rankings:
                logger.warning("未获取到任何榜单数据")
                return
            
            # 保存数据
            self.data_manager.save_rankings(rankings)
            
            # 创建可视化图表
            if self.config['save_charts']:
                self.create_charts(rankings)
            
            # 更新状态
            self.last_update = datetime.now()
            self.update_count += 1
            
            logger.info(f"榜单数据更新完成，共更新{len(rankings)}个榜单")
            
            # 清理旧数据
            if self.update_count % 10 == 0:  # 每10次更新清理一次
                self.data_manager.cleanup_old_data(self.config['auto_cleanup_days'])
            
        except Exception as e:
            logger.error(f"更新榜单数据失败: {e}")
    
    def create_charts(self, rankings: Dict):
        """创建可视化图表"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            for ranking_type, df in rankings.items():
                if df is None or df.empty:
                    continue
                
                # 创建表格
                table_fig = self.visualizer.create_ranking_table(df, ranking_type)
                if table_fig:
                    for format_type in self.config['chart_formats']:
                        self.visualizer.save_chart(
                            table_fig, 
                            f"{ranking_type}_table_{timestamp}", 
                            format_type
                        )
                
                # 创建柱状图
                bar_fig = self.visualizer.create_bar_chart(df, ranking_type)
                if bar_fig:
                    for format_type in self.config['chart_formats']:
                        self.visualizer.save_chart(
                            bar_fig, 
                            f"{ranking_type}_bar_{timestamp}", 
                            format_type
                        )
            
            logger.info("可视化图表创建完成")
            
        except Exception as e:
            logger.error(f"创建图表失败: {e}")
    
    def setup_schedule(self):
        """设置定时任务"""
        interval = self.config['update_interval_minutes']
        
        # 设置定时任务
        schedule.every(interval).minutes.do(self.update_rankings)
        
        # 设置每日清理任务
        schedule.every().day.at("02:00").do(self.daily_cleanup)
        
        # 设置每日报告任务
        schedule.every().day.at("18:00").do(self.daily_report)
        
        logger.info(f"定时任务已设置，更新间隔: {interval}分钟")
    
    def daily_cleanup(self):
        """每日清理任务"""
        try:
            logger.info("开始每日清理任务...")
            
            # 清理旧数据
            self.data_manager.cleanup_old_data(self.config['auto_cleanup_days'])
            
            # 清理旧日志文件（保留7天）
            self.cleanup_old_logs(7)
            
            logger.info("每日清理任务完成")
            
        except Exception as e:
            logger.error(f"每日清理任务失败: {e}")
    
    def cleanup_old_logs(self, days_to_keep: int):
        """清理旧日志文件"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            for log_file in self.log_dir.glob("*.log"):
                if log_file.stat().st_mtime < cutoff_date.timestamp():
                    log_file.unlink()
                    logger.info(f"已删除旧日志文件: {log_file}")
                    
        except Exception as e:
            logger.error(f"清理旧日志文件失败: {e}")
    
    def daily_report(self):
        """每日报告任务"""
        try:
            logger.info("生成每日报告...")
            
            # 获取数据摘要
            summary = self.data_manager.get_data_summary()
            
            # 获取最新数据
            latest_rankings = self.data_manager.get_latest_rankings()
            
            # 创建报告
            report = self.create_daily_report(summary, latest_rankings)
            
            # 保存报告
            report_file = self.log_dir / f"daily_report_{datetime.now().strftime('%Y%m%d')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            logger.info(f"每日报告已保存: {report_file}")
            
        except Exception as e:
            logger.error(f"生成每日报告失败: {e}")
    
    def create_daily_report(self, summary: Dict, rankings: Dict) -> Dict:
        """创建每日报告"""
        report = {
            "date": datetime.now().strftime('%Y-%m-%d'),
            "summary": summary,
            "top_rankings": {},
            "update_count": self.update_count,
            "last_update": self.last_update.strftime('%Y-%m-%d %H:%M:%S') if self.last_update else None
        }
        
        # 添加各榜单前5名
        for ranking_type, df in rankings.items():
            if not df.empty:
                top_5 = df.head(5).to_dict('records')
                report["top_rankings"][ranking_type] = top_5
        
        return report
    
    def start(self):
        """启动定时任务"""
        if self.is_running:
            logger.warning("定时任务已在运行中")
            return
        
        logger.info("启动定时任务管理器...")
        
        # 设置定时任务
        self.setup_schedule()
        
        # 立即执行一次更新
        self.update_rankings()
        
        # 启动定时任务循环
        self.is_running = True
        
        def run_scheduler():
            while self.is_running:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
        
        # 在后台线程中运行
        scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        scheduler_thread.start()
        
        logger.info("定时任务管理器已启动")
    
    def stop(self):
        """停止定时任务"""
        if not self.is_running:
            logger.warning("定时任务未在运行")
            return
        
        logger.info("停止定时任务管理器...")
        self.is_running = False
        schedule.clear()
        logger.info("定时任务管理器已停止")
    
    def get_status(self) -> Dict:
        """获取任务状态"""
        return {
            "is_running": self.is_running,
            "last_update": self.last_update.strftime('%Y-%m-%d %H:%M:%S') if self.last_update else None,
            "update_count": self.update_count,
            "next_update": schedule.next_run().strftime('%Y-%m-%d %H:%M:%S') if schedule.jobs else None,
            "config": self.config
        }

if __name__ == "__main__":
    # 测试定时任务管理器
    scheduler = RankingScheduler()
    
    print("定时任务管理器状态:")
    print(scheduler.get_status())
    
    # 启动定时任务
    scheduler.start()
    
    try:
        # 运行一段时间
        time.sleep(300)  # 运行5分钟
    except KeyboardInterrupt:
        print("\n收到停止信号")
    finally:
        scheduler.stop()
        print("定时任务已停止") 