import pandas as pd
import json
import os
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional
import sqlite3
from pathlib import Path

logger = logging.getLogger(__name__)

class DataManager:
    """数据管理器，负责数据的存储、读取和历史数据管理"""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        # 数据库文件路径
        self.db_path = self.data_dir / "rankings.db"
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建人气榜表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS popularity_ranking (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    rank INTEGER,
                    code TEXT,
                    name TEXT,
                    price REAL,
                    change_percent REAL,
                    volume INTEGER,
                    turnover REAL,
                    popularity_score INTEGER,
                    timestamp TEXT,
                    created_at TEXT
                )
            ''')
            
            # 创建飙升榜表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS soaring_ranking (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    rank INTEGER,
                    code TEXT,
                    name TEXT,
                    price REAL,
                    change_percent REAL,
                    volume INTEGER,
                    turnover REAL,
                    soaring_score INTEGER,
                    timestamp TEXT,
                    created_at TEXT
                )
            ''')
            
            # 创建话题榜表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS topic_ranking (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    rank INTEGER,
                    topic TEXT,
                    hot_score INTEGER,
                    discussion_count INTEGER,
                    related_stocks TEXT,
                    trend TEXT,
                    timestamp TEXT,
                    created_at TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            logger.info("数据库初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
    
    def save_rankings(self, rankings: Dict[str, pd.DataFrame]):
        """保存榜单数据到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            for ranking_type, df in rankings.items():
                if df is None or df.empty:
                    continue
                
                # 添加创建时间
                df_copy = df.copy()
                df_copy['created_at'] = current_time
                
                # 根据榜单类型选择表名
                table_name = f"{ranking_type}_ranking"
                
                # 保存到数据库
                df_copy.to_sql(table_name, conn, if_exists='append', index=False)
                logger.info(f"成功保存{ranking_type}榜单数据，共{len(df)}条记录")
            
            conn.close()
            
            # 同时保存为JSON文件作为备份
            self._save_json_backup(rankings, current_time)
            
        except Exception as e:
            logger.error(f"保存数据失败: {e}")
    
    def _save_json_backup(self, rankings: Dict[str, pd.DataFrame], timestamp: str):
        """保存JSON备份文件"""
        try:
            backup_data = {}
            for ranking_type, df in rankings.items():
                if df is not None and not df.empty:
                    backup_data[ranking_type] = df.to_dict('records')
            
            # 创建带时间戳的文件名
            filename = f"rankings_{timestamp.replace(':', '-').replace(' ', '_')}.json"
            filepath = self.data_dir / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"JSON备份已保存: {filepath}")
            
        except Exception as e:
            logger.error(f"保存JSON备份失败: {e}")
    
    def get_latest_rankings(self) -> Dict[str, pd.DataFrame]:
        """获取最新的榜单数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            rankings = {}
            
            # 获取最新的人气榜数据
            popularity_query = '''
                SELECT * FROM popularity_ranking 
                WHERE created_at = (SELECT MAX(created_at) FROM popularity_ranking)
                ORDER BY rank
            '''
            popularity_df = pd.read_sql_query(popularity_query, conn)
            if not popularity_df.empty:
                rankings['popularity'] = popularity_df
            
            # 获取最新的飙升榜数据
            soaring_query = '''
                SELECT * FROM soaring_ranking 
                WHERE created_at = (SELECT MAX(created_at) FROM soaring_ranking)
                ORDER BY rank
            '''
            soaring_df = pd.read_sql_query(soaring_query, conn)
            if not soaring_df.empty:
                rankings['soaring'] = soaring_df
            
            # 获取最新的话题榜数据
            topic_query = '''
                SELECT * FROM topic_ranking 
                WHERE created_at = (SELECT MAX(created_at) FROM topic_ranking)
                ORDER BY rank
            '''
            topic_df = pd.read_sql_query(topic_query, conn)
            if not topic_df.empty:
                rankings['topic'] = topic_df
            
            conn.close()
            return rankings
            
        except Exception as e:
            logger.error(f"获取最新数据失败: {e}")
            return {}
    
    def get_historical_data(self, ranking_type: str, days: int = 7) -> pd.DataFrame:
        """获取历史数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 计算时间范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            table_name = f"{ranking_type}_ranking"
            query = f'''
                SELECT * FROM {table_name}
                WHERE created_at >= '{start_date.strftime('%Y-%m-%d %H:%M:%S')}'
                ORDER BY created_at DESC, rank
            '''
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            return df
            
        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            return pd.DataFrame()
    
    def get_data_summary(self) -> Dict:
        """获取数据摘要信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            summary = {}
            
            tables = ['popularity_ranking', 'soaring_ranking', 'topic_ranking']
            for table in tables:
                # 获取记录总数
                count_query = f"SELECT COUNT(*) FROM {table}"
                total_count = conn.execute(count_query).fetchone()[0]
                
                # 获取最新更新时间
                latest_query = f"SELECT MAX(created_at) FROM {table}"
                latest_time = conn.execute(latest_query).fetchone()[0]
                
                # 获取数据时间范围
                time_range_query = f"SELECT MIN(created_at), MAX(created_at) FROM {table}"
                time_range = conn.execute(time_range_query).fetchone()
                
                summary[table.replace('_ranking', '')] = {
                    'total_records': total_count,
                    'latest_update': latest_time,
                    'time_range': time_range
                }
            
            conn.close()
            return summary
            
        except Exception as e:
            logger.error(f"获取数据摘要失败: {e}")
            return {}
    
    def cleanup_old_data(self, days_to_keep: int = 30):
        """清理旧数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cutoff_date = (datetime.now() - timedelta(days=days_to_keep)).strftime('%Y-%m-%d %H:%M:%S')
            
            tables = ['popularity_ranking', 'soaring_ranking', 'topic_ranking']
            for table in tables:
                delete_query = f"DELETE FROM {table} WHERE created_at < '{cutoff_date}'"
                deleted_count = conn.execute(delete_query).rowcount
                logger.info(f"从{table}中删除了{deleted_count}条旧记录")
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"清理旧数据失败: {e}")
    
    def export_data(self, ranking_type: str, format: str = 'csv', days: int = None) -> str:
        """导出数据"""
        try:
            if days:
                df = self.get_historical_data(ranking_type, days)
            else:
                rankings = self.get_latest_rankings()
                df = rankings.get(ranking_type, pd.DataFrame())
            
            if df.empty:
                return ""
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            if format.lower() == 'csv':
                filename = f"{ranking_type}_{timestamp}.csv"
                filepath = self.data_dir / filename
                df.to_csv(filepath, index=False, encoding='utf-8-sig')
            elif format.lower() == 'excel':
                filename = f"{ranking_type}_{timestamp}.xlsx"
                filepath = self.data_dir / filename
                df.to_excel(filepath, index=False)
            else:
                raise ValueError(f"不支持的导出格式: {format}")
            
            logger.info(f"数据已导出到: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"导出数据失败: {e}")
            return ""

if __name__ == "__main__":
    # 测试数据管理器
    dm = DataManager()
    
    # 获取数据摘要
    summary = dm.get_data_summary()
    print("数据摘要:")
    for key, value in summary.items():
        print(f"{key}: {value}") 