# 东方财富榜单数据爬取与可视化项目

## 项目简介

本项目是一个自动化数据爬取和可视化系统，专门用于获取东方财富网的人气榜、飙升榜和话题榜数据，并提供实时可视化和历史数据分析功能。

## 功能特性

- 🔄 **自动数据爬取**: 定时从东方财富网获取最新榜单数据
- 📊 **实时可视化**: 提供交互式图表和表格展示
- 💾 **数据持久化**: 使用SQLite数据库存储历史数据
- 📈 **历史分析**: 支持查看历史趋势和变化
- 🌐 **Web仪表板**: 基于Dash的现代化Web界面
- ⏰ **定时任务**: 自动更新数据和清理旧数据
- 📱 **响应式设计**: 支持多种设备访问

## 安装说明

### 1. 环境要求
- Python 3.8+
- Windows/Linux/macOS

### 2. 安装依赖
```bash
# 进入项目目录
cd 数据爬取可视化项目

# 安装依赖包
pip install -r requirements.txt
```

### 3. 验证安装
```bash
# 测试爬虫功能
python main.py --mode test
```

## 运行说明

### 方式一：启动Web仪表板（推荐）
```bash
python main.py --mode web
```
- 访问地址: http://localhost:8050
- 功能：实时数据展示、历史分析、系统状态监控

### 方式二：运行定时任务
```bash
python main.py --mode scheduler
```
- 功能：后台自动更新数据，无需人工干预

### 方式三：单次数据更新
```bash
python main.py --mode once
```
- 功能：执行一次数据爬取和可视化

### 方式四：查看系统状态
```bash
python main.py --mode status
```
- 功能：显示当前数据状态和系统信息

### 方式五：导出数据
```bash
# 导出人气榜数据为CSV格式
python main.py --mode export --type popularity --format csv

# 导出话题榜数据为JSON格式，最近7天
python main.py --mode export --type topic --format json --days 7
```

## 项目结构

```
数据爬取可视化项目/
├── main.py                 # 主程序入口
├── eastmoney_crawler.py    # 数据爬取模块
├── data_manager.py         # 数据管理模块
├── visualizer.py           # 可视化模块
├── scheduler.py            # 定时任务模块
├── web_dashboard.py        # Web仪表板
├── requirements.txt        # 依赖包列表
├── README.md              # 项目说明
├── data/                  # 数据存储目录
│   └── rankings.db        # SQLite数据库
├── charts/                # 图表输出目录
└── logs/                  # 日志目录
```

## 配置说明

### 定时任务配置
系统会自动创建 `scheduler_config.json` 配置文件，包含：
- 数据更新频率
- 数据保留天数
- 交易时间设置
- 日志级别

### 自定义配置
可以修改 `scheduler_config.json` 来调整：
```json
{
    "update_interval_minutes": 30,
    "data_retention_days": 30,
    "trading_hours": {
        "start": "09:30",
        "end": "15:00"
    },
    "log_level": "INFO"
}
```

## 使用指南

### Web仪表板使用
1. 启动Web服务后，在浏览器中访问 http://localhost:8050
2. **实时数据** 标签页：查看最新的榜单数据
3. **历史数据** 标签页：分析历史趋势
4. **统计信息** 标签页：查看数据统计和变化
5. **系统设置** 标签页：监控系统状态和配置

### 数据说明
- **人气榜**: 基于用户关注度的股票排名
- **飙升榜**: 短时间内涨幅较大的股票
- **话题榜**: 热门讨论的股票话题

## 故障排除

### 常见问题
1. **爬取失败**: 检查网络连接，系统会自动使用模拟数据
2. **端口占用**: 修改 `web_dashboard.py` 中的端口号
3. **权限错误**: 确保对项目目录有读写权限

### 日志查看
```bash
# 查看最新日志
tail -f logs/ranking_system.log
```

## 技术栈

- **爬虫**: requests, selenium, beautifulsoup4
- **数据处理**: pandas, numpy
- **可视化**: plotly, dash
- **数据库**: SQLite
- **定时任务**: schedule
- **Web框架**: Dash, Bootstrap

## 更新日志

- v1.0.0: 初始版本，支持基础爬取和可视化功能
- 支持人气榜、飙升榜、话题榜数据获取
- 提供Web仪表板和定时任务功能

## 许可证

本项目仅供学习和研究使用，请遵守相关网站的使用条款。 