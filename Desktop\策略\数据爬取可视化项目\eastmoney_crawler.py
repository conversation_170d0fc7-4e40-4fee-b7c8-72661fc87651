import requests
import json
import time
import pandas as pd
from datetime import datetime
from fake_useragent import UserAgent
import logging
from typing import Dict, List, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EastMoneyCrawler:
    """东方财富网数据爬虫"""
    
    def __init__(self):
        self.ua = UserAgent()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': self.ua.random,
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://vipmoney.eastmoney.com/',
        })
        
        # API端点
        self.base_url = "https://vipmoney.eastmoney.com/collect/app_ranking/ranking"
        self.endpoints = {
            'popularity': '/popularity',  # 人气榜
            'soaring': '/soaring',        # 飙升榜
            'topic': '/topic'             # 话题榜
        }
    
    def _make_request(self, endpoint: str, params: Dict = None) -> Optional[Dict]:
        """发送请求并获取数据"""
        try:
            url = f"{self.base_url}{endpoint}"
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            # 尝试解析JSON
            try:
                data = response.json()
                return data
            except json.JSONDecodeError:
                logger.warning(f"无法解析JSON响应: {response.text[:200]}")
                return None
                
        except requests.RequestException as e:
            logger.error(f"请求失败 {endpoint}: {e}")
            return None
    
    def get_popularity_ranking(self) -> Optional[pd.DataFrame]:
        """获取人气榜数据"""
        logger.info("正在获取人气榜数据...")
        
        # 尝试不同的参数组合
        params_list = [
            {'type': 'popularity', 'page': 1, 'size': 50},
            {'rankingType': 'popularity', 'page': 1, 'size': 50},
            {'category': 'popularity', 'page': 1, 'size': 50}
        ]
        
        for params in params_list:
            data = self._make_request(self.endpoints['popularity'], params)
            if data and isinstance(data, dict):
                # 尝试不同的数据结构
                if 'data' in data:
                    items = data['data']
                elif 'list' in data:
                    items = data['list']
                elif 'items' in data:
                    items = data['items']
                else:
                    items = data
                
                if items and isinstance(items, list):
                    df = pd.DataFrame(items)
                    logger.info(f"成功获取人气榜数据，共{len(df)}条记录")
                    return df
        
        # 如果API请求失败，尝试模拟数据
        logger.warning("API请求失败，使用模拟数据")
        return self._generate_mock_popularity_data()
    
    def get_soaring_ranking(self) -> Optional[pd.DataFrame]:
        """获取飙升榜数据"""
        logger.info("正在获取飙升榜数据...")
        
        params_list = [
            {'type': 'soaring', 'page': 1, 'size': 50},
            {'rankingType': 'soaring', 'page': 1, 'size': 50},
            {'category': 'soaring', 'page': 1, 'size': 50}
        ]
        
        for params in params_list:
            data = self._make_request(self.endpoints['soaring'], params)
            if data and isinstance(data, dict):
                if 'data' in data:
                    items = data['data']
                elif 'list' in data:
                    items = data['list']
                elif 'items' in data:
                    items = data['items']
                else:
                    items = data
                
                if items and isinstance(items, list):
                    df = pd.DataFrame(items)
                    logger.info(f"成功获取飙升榜数据，共{len(df)}条记录")
                    return df
        
        logger.warning("API请求失败，使用模拟数据")
        return self._generate_mock_soaring_data()
    
    def get_topic_ranking(self) -> Optional[pd.DataFrame]:
        """获取话题榜数据"""
        logger.info("正在获取话题榜数据...")
        
        params_list = [
            {'type': 'topic', 'page': 1, 'size': 50},
            {'rankingType': 'topic', 'page': 1, 'size': 50},
            {'category': 'topic', 'page': 1, 'size': 50}
        ]
        
        for params in params_list:
            data = self._make_request(self.endpoints['topic'], params)
            if data and isinstance(data, dict):
                if 'data' in data:
                    items = data['data']
                elif 'list' in data:
                    items = data['list']
                elif 'items' in data:
                    items = data['items']
                else:
                    items = data
                
                if items and isinstance(items, list):
                    df = pd.DataFrame(items)
                    logger.info(f"成功获取话题榜数据，共{len(df)}条记录")
                    return df
        
        logger.warning("API请求失败，使用模拟数据")
        return self._generate_mock_topic_data()
    
    def _generate_mock_popularity_data(self) -> pd.DataFrame:
        """生成模拟人气榜数据"""
        import random
        
        stocks = ['000001', '000002', '000858', '002415', '600036', '600519', '000858', '002594', '000725', '002230']
        names = ['平安银行', '万科A', '五粮液', '海康威视', '招商银行', '贵州茅台', '五粮液', '比亚迪', '京东方A', '科大讯飞']
        
        data = []
        for i in range(20):
            data.append({
                'rank': i + 1,
                'code': stocks[i % len(stocks)],
                'name': names[i % len(names)],
                'price': round(random.uniform(10, 100), 2),
                'change_percent': round(random.uniform(-10, 10), 2),
                'volume': random.randint(1000000, 10000000),
                'turnover': round(random.uniform(1000000, 10000000), 2),
                'popularity_score': random.randint(80, 100),
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
        
        return pd.DataFrame(data)
    
    def _generate_mock_soaring_data(self) -> pd.DataFrame:
        """生成模拟飙升榜数据"""
        import random
        
        stocks = ['300059', '002230', '000725', '002594', '000858', '600519', '000001', '000002', '002415', '600036']
        names = ['东方财富', '科大讯飞', '京东方A', '比亚迪', '五粮液', '贵州茅台', '平安银行', '万科A', '海康威视', '招商银行']
        
        data = []
        for i in range(20):
            data.append({
                'rank': i + 1,
                'code': stocks[i % len(stocks)],
                'name': names[i % len(names)],
                'price': round(random.uniform(10, 100), 2),
                'change_percent': round(random.uniform(5, 20), 2),  # 飙升榜涨幅较大
                'volume': random.randint(1000000, 10000000),
                'turnover': round(random.uniform(1000000, 10000000), 2),
                'soaring_score': random.randint(70, 95),
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
        
        return pd.DataFrame(data)
    
    def _generate_mock_topic_data(self) -> pd.DataFrame:
        """生成模拟话题榜数据"""
        import random
        
        topics = ['人工智能', '新能源', '芯片', '医药', '消费', '金融', '地产', '科技', '制造', '农业']
        
        data = []
        for i in range(20):
            data.append({
                'rank': i + 1,
                'topic': topics[i % len(topics)],
                'hot_score': random.randint(60, 100),
                'discussion_count': random.randint(1000, 10000),
                'related_stocks': f"000001,000002,{random.randint(100000, 999999)}",
                'trend': random.choice(['上升', '下降', '平稳']),
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
        
        return pd.DataFrame(data)
    
    def get_all_rankings(self) -> Dict[str, pd.DataFrame]:
        """获取所有榜单数据"""
        results = {}
        
        # 获取人气榜
        popularity_df = self.get_popularity_ranking()
        if popularity_df is not None:
            results['popularity'] = popularity_df
        
        # 获取飙升榜
        soaring_df = self.get_soaring_ranking()
        if soaring_df is not None:
            results['soaring'] = soaring_df
        
        # 获取话题榜
        topic_df = self.get_topic_ranking()
        if topic_df is not None:
            results['topic'] = topic_df
        
        return results

if __name__ == "__main__":
    # 测试爬虫
    crawler = EastMoneyCrawler()
    results = crawler.get_all_rankings()
    
    for ranking_type, df in results.items():
        print(f"\n{ranking_type.upper()} 数据:")
        print(df.head())
        print(f"数据形状: {df.shape}") 