import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
import numpy as np
from datetime import datetime
import logging
from typing import Dict, List, Optional
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class RankingVisualizer:
    """榜单数据可视化器"""
    
    def __init__(self, output_dir: str = "charts"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 颜色配置
        self.colors = {
            'popularity': '#FF6B6B',
            'soaring': '#4ECDC4',
            'topic': '#45B7D1',
            'positive': '#2ECC71',
            'negative': '#E74C3C',
            'neutral': '#95A5A6'
        }
    
    def create_ranking_table(self, df: pd.DataFrame, ranking_type: str) -> go.Figure:
        """创建榜单表格"""
        if df.empty:
            return go.Figure()
        
        # 根据榜单类型选择显示列
        if ranking_type == 'popularity':
            columns = ['rank', 'name', 'code', 'price', 'change_percent', 'popularity_score']
            column_names = ['排名', '股票名称', '代码', '价格', '涨跌幅(%)', '人气指数']
        elif ranking_type == 'soaring':
            columns = ['rank', 'name', 'code', 'price', 'change_percent', 'soaring_score']
            column_names = ['排名', '股票名称', '代码', '价格', '涨跌幅(%)', '飙升指数']
        elif ranking_type == 'topic':
            columns = ['rank', 'topic', 'hot_score', 'discussion_count', 'trend']
            column_names = ['排名', '话题', '热度指数', '讨论数', '趋势']
        else:
            return go.Figure()
        
        # 准备数据
        data = []
        for _, row in df.head(20).iterrows():
            data_row = []
            for col in columns:
                if col == 'change_percent':
                    value = f"{row[col]:.2f}%"
                elif col in ['popularity_score', 'soaring_score', 'hot_score']:
                    value = f"{row[col]}"
                else:
                    value = str(row[col])
                data_row.append(value)
            data.append(data_row)
        
        # 创建表格
        fig = go.Figure(data=[go.Table(
            header=dict(
                values=column_names,
                fill_color=self.colors[ranking_type],
                font=dict(color='white', size=14),
                align='center'
            ),
            cells=dict(
                values=list(zip(*data)),
                fill_color='white',
                font=dict(color='black', size=12),
                align='center',
                height=30
            )
        )])
        
        fig.update_layout(
            title=f"{ranking_type.title()}榜单",
            height=600,
            margin=dict(l=20, r=20, t=40, b=20)
        )
        
        return fig
    
    def create_bar_chart(self, df: pd.DataFrame, ranking_type: str) -> go.Figure:
        """创建柱状图"""
        if df.empty:
            return go.Figure()
        
        top_10 = df.head(10)
        
        if ranking_type in ['popularity', 'soaring']:
            score_col = f'{ranking_type}_score'
            name_col = 'name'
        elif ranking_type == 'topic':
            score_col = 'hot_score'
            name_col = 'topic'
        else:
            return go.Figure()
        
        fig = go.Figure(data=[
            go.Bar(
                x=top_10[name_col],
                y=top_10[score_col],
                marker_color=self.colors[ranking_type],
                text=top_10[score_col],
                textposition='auto',
            )
        ])
        
        fig.update_layout(
            title=f"{ranking_type.title()}榜前10名",
            xaxis_title="名称",
            yaxis_title="指数",
            height=500,
            xaxis_tickangle=-45
        )
        
        return fig
    
    def save_chart(self, fig: go.Figure, filename: str, format: str = 'html'):
        """保存图表"""
        try:
            filepath = self.output_dir / f"{filename}.{format}"
            
            if format == 'html':
                fig.write_html(filepath)
            elif format == 'png':
                fig.write_image(filepath)
            else:
                raise ValueError(f"不支持的格式: {format}")
            
            logger.info(f"图表已保存: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"保存图表失败: {e}")
            return "" 